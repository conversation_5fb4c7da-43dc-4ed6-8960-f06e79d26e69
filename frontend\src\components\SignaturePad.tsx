import React, { useRef, useEffect, useState } from 'react';
import SignatureCanvas from 'react-signature-canvas';
import { useMobile } from '../hooks/useMobile';

interface SignaturePadProps {
  value?: string;
  onChange: (signature: string) => void;
  disabled?: boolean;
  label?: string;
  width?: number;
  height?: number;
}

const SignaturePad: React.FC<SignaturePadProps> = ({
  value,
  onChange,
  disabled = false,
  label = 'Handtekening',
  width = 400,
  height = 200
}) => {
  const sigRef = useRef<SignatureCanvas>(null);
  const [isEmpty, setIsEmpty] = useState(true);
  const { isMobile } = useMobile();

  // Adjust dimensions for mobile
  const canvasWidth = isMobile ? Math.min(width, 350) : width;
  const canvasHeight = isMobile ? Math.min(height, 150) : height;

  useEffect(() => {
    if (value && sigRef.current) {
      try {
        sigRef.current.fromDataURL(value);
        setIsEmpty(false);
      } catch (error) {
        console.error('Error loading signature:', error);
      }
    }
  }, [value]);

  const handleClear = () => {
    if (sigRef.current) {
      sigRef.current.clear();
      setIsEmpty(true);
      onChange('');
    }
  };

  const handleEnd = () => {
    if (sigRef.current) {
      const dataURL = sigRef.current.toDataURL('image/png');
      const empty = sigRef.current.isEmpty();
      setIsEmpty(empty);
      onChange(empty ? '' : dataURL);
    }
  };

  return (
    <div className="signature-pad-container">
      <label className="label">
        <span className="label-text text-amspm-text dark:text-dark-text">
          {label}
        </span>
      </label>
      
      <div className="border border-gray-300 dark:border-gray-600 rounded-lg p-2 bg-white dark:bg-gray-800">
        <SignatureCanvas
          ref={sigRef}
          canvasProps={{
            width: canvasWidth,
            height: canvasHeight,
            className: 'signature-canvas border border-gray-200 dark:border-gray-700 rounded mobile-touch-target',
            style: {
              width: '100%',
              height: `${canvasHeight}px`,
              maxWidth: `${canvasWidth}px`,
              backgroundColor: disabled ? '#f5f5f5' : 'white',
              touchAction: 'none' // Prevent scrolling while drawing
            }
          }}
          onEnd={handleEnd}
          backgroundColor={disabled ? '#f5f5f5' : 'white'}
          penColor="#000000"
          minWidth={1}
          maxWidth={3}
          velocityFilterWeight={0.7}
          disabled={disabled}
        />
        
        <div className="flex justify-between items-center mt-2">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {isEmpty ? 'Teken uw handtekening hierboven' : 'Handtekening vastgelegd'}
          </div>
          
          <button
            type="button"
            onClick={handleClear}
            disabled={disabled || isEmpty}
            className="btn btn-sm btn-outline btn-error mobile-touch-target"
          >
            Wissen
          </button>
        </div>
      </div>
    </div>
  );
};

export default SignaturePad;
